@import url("https://fonts.googleapis.com/css2?family=Work+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.cdnfonts.com/css/pricedown");
@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Yantramanav:wght@100;300;400;500;700;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Work+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Yantramanav:wght@100;300;400;500;700;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");

/* Modern CSS Variables for consistent theming */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
    --shadow-heavy: 0 15px 35px rgba(0, 0, 0, 0.3);
    --blur-effect: blur(10px);
    --border-radius: 16px;
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Modern Menu with Glassmorphism */
div#openmenu {
    display: none;
    position: absolute;
    width: 55%;
    top: 45%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: menuSlideIn 0.5s var(--transition-bounce);
}

@keyframes menuSlideIn {
    0% {
        opacity: 0;
        transform: translate(-50%, -60%) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.brand-logo {
    width: 25%;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    transition: var(--transition-smooth);
}

.brand-logo:hover {
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.4));
    transform: scale(1.05);
}

/* Modern Menu tab & panel styles with Glassmorphism */
.text-tabcolor {
    color: rgb(255, 255, 255) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.bg-active-tab {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--blur-effect);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-light);
}

.bg-panel {
    background: linear-gradient(135deg, rgba(30, 30, 30, 0.9) 0%, rgba(45, 45, 45, 0.8) 100%) !important;
    backdrop-filter: var(--blur-effect);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-heavy);
}

.q-tab-panels {
    border-top-right-radius: var(--border-radius) !important;
    border-bottom-right-radius: var(--border-radius) !important;
    overflow: hidden;
}

.q-splitter__panel.q-splitter__before {
    border-top-left-radius: var(--border-radius) !important;
    border-bottom-left-radius: var(--border-radius) !important;
    overflow: hidden;
}

.q-tabs--vertical.q-tabs--not-scrollable .q-tabs__content {
    border-top-left-radius: var(--border-radius) !important;
    border-bottom-left-radius: var(--border-radius) !important;
    overflow: hidden;
}

/* Modern Menu text styles */
.text-h6 {
    color: rgb(255, 255, 255) !important;
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-h7 {
    color: rgba(255, 255, 255, 0.85) !important;
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Modern Menu buttons & checkboxes & toggle switch icons styles */
.text-textbutton {
    color: rgb(255, 255, 255) !important;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.bg-bgbutton {
    background: var(--primary-gradient) !important;
    border: none;
    box-shadow: var(--shadow-light);
    transition: var(--transition-smooth);
}

.bg-bgbutton:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
}

.q-checkbox__inner {
    color: rgb(255 255 255 / 80%) !important;
    transition: var(--transition-smooth);
}

.text-checkbox {
    color: rgb(102, 126, 234) !important;
}

.q-checkbox__svg {
    color: rgb(255, 255, 255) !important;
}

.q-checkbox__inner--truthy .q-checkbox__bg {
    background: var(--primary-gradient) !important;
    box-shadow: 0px 0px 15px 5px rgba(102, 126, 234, 0.3) !important;
    border: none;
}

.text-toggleicons {
    color: rgb(255, 255, 255) !important;
}

/* Modern Menu misc */
hr {
    opacity: 0.2 !important;
    background: var(--primary-gradient);
    height: 2px;
    border: none;
    border-radius: 1px;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.q-mb-md {
    margin-bottom: 0px !important;
}

.q-mb-md-d {
    padding-left: 10px !important;
    padding-bottom: 10px !important;
}

.q-item {
    padding: 12px 8px !important;
    border-radius: 8px;
    margin: 4px 0;
    transition: var(--transition-smooth);
}

.q-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(4px);
}

.q-item__section--avatar {
    min-width: 40px !important;
}

.q-item__section--side {
    padding-right: 0px !important;
}

.q-splitter--vertical > .q-splitter__separator {
    width: 0px !important;
}

div#q-loading-bar {
    display: none !important;
}

::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-gradient);
}

/* Modern HUD radial styles with gradients */
.text-health {
    color: rgb(76, 175, 80) !important;
}
.text-stress {
    color: rgb(244, 67, 54) !important;
}
.text-nos {
    color: rgb(233, 30, 99) !important;
}
.text-cruise {
    color: rgb(156, 39, 176) !important;
}
.text-armed {
    color: rgb(255, 152, 0) !important;
}
.text-harness {
    color: rgb(103, 58, 183) !important;
}
.text-oxygen {
    color: rgb(33, 150, 243) !important;
}
.text-parachute {
    color: rgb(96, 125, 139) !important;
}
.text-dev {
    color: rgb(121, 85, 72) !important;
}
.text-gauge {
    color: rgb(255, 255, 255) !important;
}

#main-container {
    width: 100%;
    height: auto;
}

/* Modern Money Display with Glassmorphism */
#money-container {
    position: absolute;
    right: 2vw;
    top: 5vh;
    font-weight: 400;
    font-size: 40px;
    background: var(--glass-bg);
    backdrop-filter: var(--blur-effect);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 20px 25px;
    box-shadow: var(--shadow-light);
    animation: moneySlideIn 0.6s var(--transition-bounce);
}

@keyframes moneySlideIn {
    0% {
        opacity: 0;
        transform: translateX(100px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

#money-container:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    transition: var(--transition-smooth);
}

#sign,
#bank {
    font-family: "Inter", sans-serif;
    text-align: right;
    color: #4CAF50;
    font-weight: 700;
    text-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
    background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

#plus {
    font-size: 50px;
    font-family: "Inter", sans-serif;
    text-align: right;
    color: #4CAF50;
    font-weight: 800;
    text-shadow: 0 3px 10px rgba(76, 175, 80, 0.5);
    animation: pulseGreen 2s infinite;
}

@keyframes pulseGreen {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

#minus {
    font-size: 50px;
    font-family: "Inter", sans-serif;
    text-align: right;
    color: #F44336;
    font-weight: 800;
    text-shadow: 0 3px 10px rgba(244, 67, 54, 0.5);
    animation: pulseRed 2s infinite;
}

@keyframes pulseRed {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

#money {
    font-family: "Inter", sans-serif;
    text-align: right;
    color: #ffffff;
    font-weight: 600;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

/* Modern Player HUD with Enhanced Animations */

#playerhud {
    position: absolute;
    display: flex;
    left: 3vh;
    bottom: 0.2vw;
    animation: hudSlideUp 0.8s var(--transition-bounce);
}

@keyframes hudSlideUp {
    0% {
        opacity: 0;
        transform: translateY(50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.q-ml-xl {
    margin-left: -22px !important;
}

/* Enhanced circular progress animations */
.q-circular-progress {
    transition: var(--transition-smooth);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.q-circular-progress:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.4));
}

/* Pulsing effect for low values */
@keyframes pulseLow {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 4px 8px rgba(244, 67, 54, 0.3));
    }
    50% {
        transform: scale(1.05);
        filter: drop-shadow(0 6px 12px rgba(244, 67, 54, 0.6));
    }
}

.low-value {
    animation: pulseLow 2s infinite;
}

/* Vehicle HUD */

speed:after {
    content: "MPH"; /* If using KPH change this content from MPH */
    display: block;
    padding-top: 3px;
    padding-left: 2px;
    padding-right: 2px;
    padding-bottom: 8px;
    font-weight: 900;
}

altitude:after {
    content: "ALT";
    display: block;
    padding-top: 10px;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: 900;
}

speed {
    transform: rotate(150deg);
    font-size: 2.4vh;
    position: fixed;
    color: #fff;
    text-align: center;
    font-weight: 600;
    text-shadow: -1px -1px 0 rgba(0, 0, 0, 0.7), 1px -1px 0 rgba(0, 0, 0, 0.7), -1px 1px 0 rgba(0, 0, 0, 0.7), 1px 1px 0 rgba(0, 0, 0, 0.7);
}

altitude {
    transform: rotate(135deg);
    font-size: 2.4vh;
    position: fixed;
    color: #fff;
    text-align: center;
    font-weight: 600;
    text-shadow: -1px -1px 0 rgba(0, 0, 0, 0.7), 1px -1px 0 rgba(0, 0, 0, 0.7), -1px 1px 0 rgba(0, 0, 0, 0.7), 1px 1px 0 rgba(0, 0, 0, 0.7);
}

.border {
    bottom: 7.9%;
    left: 1.3%;
    width: 0%;
    text-align: center;
}

.square {
    bottom: 6.3%;
    width: 29vh;
    height: 18.5vh;
    border: 4px solid #bababa;
    position: absolute;
    display: inline-block;
}

.circle {
    bottom: 6.9%;
    width: 27vh;
    height: 22.9vh;
    border: 4px solid #bababa;
    position: absolute;
    display: inline-block;
    border-radius: 50%;
}

/* Modern Enhanced Animations */
.slide-fade-enter-active {
    transition: all 0.4s var(--transition-bounce);
}

.slide-fade-leave-active {
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
    transform: translateX(30px) scale(0.9);
    opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
    transition: all 0.5s var(--transition-smooth);
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
    transform: scale(0.95);
}

/* New Modern Animations */
@keyframes breathe {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
    }
}

@keyframes slideInFromLeft {
    0% {
        opacity: 0;
        transform: translateX(-100px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    0% {
        opacity: 0;
        transform: translateX(100px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

.breathe { animation: breathe 3s ease-in-out infinite; }
.glow { animation: glow 2s ease-in-out infinite; }
.slide-in-left { animation: slideInFromLeft 0.6s var(--transition-bounce); }
.slide-in-right { animation: slideInFromRight 0.6s var(--transition-bounce); }
.bounce-in { animation: bounceIn 0.8s var(--transition-bounce); }
.shake { animation: shake 0.5s ease-in-out; }

/* Modern Compass with Glassmorphism */
.baseplate {
    position: relative;
    margin: 0 auto;
    top: -0.8vh;
    width: 160px;
    height: auto;
    background: var(--glass-bg);
    backdrop-filter: var(--blur-effect);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 15px;
    box-shadow: var(--shadow-light);
    animation: compassSlideDown 0.7s var(--transition-bounce);
}

@keyframes compassSlideDown {
    0% {
        opacity: 0;
        transform: translateY(-50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.street-container {
    position: relative;
    top: 0.5vh;
    font-family: "Inter", sans-serif;
    font-size: 1.5vh;
    letter-spacing: 1px;
    font-weight: 700;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    animation: streetFadeIn 1s ease-out 0.3s both;
}

@keyframes streetFadeIn {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.street1 {
    position: absolute;
    margin: 0 auto;
    right: 55%;
    text-align: right !important;
    color: rgb(255, 255, 255);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.street2 {
    position: absolute;
    margin: 0 auto;
    left: 55%;
    text-align: left !important;
    color: rgb(255, 255, 255);
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.pointer {
    position: absolute;
    margin: 0 auto;
    top: -2%;
    left: 0;
    right: 0;
    font-family: "Inter", sans-serif;
    color: #FFD700;
    font-size: 2.4vh;
    font-weight: 800;
    text-align: center;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
    z-index: 9999;
    animation: pointerPulse 2s ease-in-out infinite;
}

@keyframes pointerPulse {
    0%, 100% {
        transform: scale(1);
        text-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
    }
    50% {
        transform: scale(1.1);
        text-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    }
}

.degrees {
    position: absolute;
    margin: 0 auto;
    top: 80%;
    left: 0;
    right: 0;
    opacity: 0.9;
    font-family: "Inter", sans-serif;
    color: rgb(255, 255, 255);
    font-size: 1.6vh;
    font-weight: 600;
    text-align: center;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    background: var(--success-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bezel {
    position: relative;
    width: 100%;
    height: 2vh;
    font-family: "Inter", sans-serif;
    font-size: 0.4vh;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.1));
}

.bearing {
    position: relative;
    width: 100%;
    top: -0.5vh;
    height: 3.5vh;
    padding-left: 0.12vw;
    font-family: "Inter", sans-serif;
    font-size: 2.4vh;
    letter-spacing: 1px;
    font-weight: 700;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.1));
}

/* Modern Vehicle HUD */
speed:after {
    content: "MPH"; /* If using KPH change this content from MPH */
    display: block;
    padding-top: 3px;
    padding-left: 2px;
    padding-right: 2px;
    padding-bottom: 8px;
    font-weight: 900;
    font-family: "Inter", sans-serif;
    background: var(--warning-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

altitude:after {
    content: "ALT";
    display: block;
    padding-top: 10px;
    padding-left: 2px;
    padding-right: 2px;
    font-weight: 900;
    font-family: "Inter", sans-serif;
    background: var(--success-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

speed {
    transform: rotate(150deg);
    font-size: 2.6vh;
    position: fixed;
    color: #fff;
    text-align: center;
    font-weight: 700;
    font-family: "Inter", sans-serif;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: speedPulse 3s ease-in-out infinite;
}

@keyframes speedPulse {
    0%, 100% { transform: rotate(150deg) scale(1); }
    50% { transform: rotate(150deg) scale(1.02); }
}

altitude {
    transform: rotate(135deg);
    font-size: 2.6vh;
    position: fixed;
    color: #fff;
    text-align: center;
    font-weight: 700;
    font-family: "Inter", sans-serif;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    background: var(--success-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Modern Vehicle HUD Container */
#veh-container {
    animation: vehicleHudSlideIn 0.8s var(--transition-bounce);
}

@keyframes vehicleHudSlideIn {
    0% {
        opacity: 0;
        transform: translateY(100px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Speedometer */
#speedometer {
    filter: drop-shadow(0 8px 16px rgba(102, 126, 234, 0.3));
    transition: var(--transition-smooth);
}

#speedometer:hover {
    filter: drop-shadow(0 12px 24px rgba(102, 126, 234, 0.5));
    transform: scale(1.02);
}

/* Enhanced Fuel Gauge */
#fuelgauge {
    filter: drop-shadow(0 8px 16px rgba(255, 152, 0, 0.3));
    transition: var(--transition-smooth);
}

#fuelgauge:hover {
    filter: drop-shadow(0 12px 24px rgba(255, 152, 0, 0.5));
    transform: scale(1.02);
}

/* Enhanced Altitude Gauge */
#altitudegauge {
    filter: drop-shadow(0 8px 16px rgba(76, 175, 80, 0.3));
    transition: var(--transition-smooth);
}

#altitudegauge:hover {
    filter: drop-shadow(0 12px 24px rgba(76, 175, 80, 0.5));
    transform: scale(1.02);
}

/* Enhanced Seatbelt */
#seatbelt {
    filter: drop-shadow(0 8px 16px rgba(244, 67, 54, 0.3));
    transition: var(--transition-smooth);
}

#seatbelt:hover {
    filter: drop-shadow(0 12px 24px rgba(244, 67, 54, 0.5));
    transform: scale(1.02);
}

.border {
    bottom: 7.9%;
    left: 1.3%;
    width: 0%;
    text-align: center;
}

.square {
    bottom: 6.3%;
    width: 29vh;
    height: 18.5vh;
    border: 4px solid transparent;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
    border-image: var(--primary-gradient) 1;
    position: absolute;
    display: inline-block;
    box-shadow: var(--shadow-light);
    backdrop-filter: var(--blur-effect);
    animation: borderGlow 3s ease-in-out infinite;
}

.circle {
    bottom: 6.9%;
    width: 27vh;
    height: 22.9vh;
    border: 4px solid transparent;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
    border-image: var(--primary-gradient) 1;
    position: absolute;
    display: inline-block;
    border-radius: 50%;
    box-shadow: var(--shadow-light);
    backdrop-filter: var(--blur-effect);
    animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
    0%, 100% {
        box-shadow: var(--shadow-light);
    }
    50% {
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.6);
    }
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: rippleAnimation 0.6s linear;
    pointer-events: none;
}

@keyframes rippleAnimation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Interactive Elements Enhancement */
.q-btn, .q-checkbox, .q-toggle {
    position: relative;
    overflow: hidden;
}

/* Pulse Animation for Critical Values */
@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 var(--pulse-color, rgba(102, 126, 234, 0.7));
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

/* Loading Animation */
@keyframes loading {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading {
    animation: loading 2s linear infinite;
}

/* Notification Slide In */
@keyframes notificationSlideIn {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-enter {
    animation: notificationSlideIn 0.5s var(--transition-bounce);
}
