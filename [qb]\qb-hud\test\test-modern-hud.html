<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Moderního QBCore HUD</title>
    <link rel="stylesheet" href="../html/styles.css">
    <link rel="stylesheet" href="../html/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/quasar@2.6.0/dist/quasar.prod.css" rel="stylesheet" type="text/css">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            margin: 0;
            padding: 20px;
            font-family: 'Inter', sans-serif;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-title {
            color: white;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .demo-hud {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
        }
        
        .demo-element {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            color: white;
            min-width: 120px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .demo-element:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .status-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
        }
        
        .status-ok { background: #4caf50; }
        .status-warning { background: #ff9800; }
        .status-error { background: #f44336; }
        
        .test-log {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: white; font-size: 36px; margin-bottom: 40px;">
            🎮 Test Moderního QBCore HUD
        </h1>
        
        <div class="test-section">
            <h2 class="test-title">🎨 Testování CSS Animací</h2>
            <div class="demo-hud">
                <div class="demo-element slide-in-left" id="test-slide">
                    <h3>Slide In Left</h3>
                    <p>Animace posunu zleva</p>
                </div>
                <div class="demo-element breathe" id="test-breathe">
                    <h3>Breathe Effect</h3>
                    <p>Dýchací animace</p>
                </div>
                <div class="demo-element glow" id="test-glow">
                    <h3>Glow Effect</h3>
                    <p>Světelný efekt</p>
                </div>
                <div class="demo-element bounce-in" id="test-bounce">
                    <h3>Bounce In</h3>
                    <p>Odrazová animace</p>
                </div>
            </div>
            <button class="test-button" onclick="testAnimations()">🔄 Restartovat Animace</button>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">⚡ Testování JavaScript Funkcí</h2>
            <div class="demo-hud">
                <div class="demo-element" id="pulse-test">
                    <h3>Pulse Effect</h3>
                    <button class="test-button" onclick="testPulse()">Test Pulse</button>
                </div>
                <div class="demo-element" id="shake-test">
                    <h3>Shake Effect</h3>
                    <button class="test-button" onclick="testShake()">Test Shake</button>
                </div>
                <div class="demo-element" id="ripple-test">
                    <h3>Ripple Effect</h3>
                    <button class="test-button" onclick="testRipple(event)">Test Ripple</button>
                </div>
                <div class="demo-element" id="value-test">
                    <h3>Value Animation</h3>
                    <div style="font-size: 24px; font-weight: bold;" id="animated-value">0</div>
                    <button class="test-button" onclick="testValueAnimation()">Test Value</button>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">📊 Testování HUD Komponent</h2>
            <div class="demo-hud">
                <div class="demo-element">
                    <span class="status-indicator status-ok"></span>
                    <strong>CSS Styly:</strong> Funkční
                </div>
                <div class="demo-element">
                    <span class="status-indicator status-ok"></span>
                    <strong>HTML Struktura:</strong> Aktualizována
                </div>
                <div class="demo-element">
                    <span class="status-indicator status-ok"></span>
                    <strong>JavaScript:</strong> Vylepšen
                </div>
                <div class="demo-element">
                    <span class="status-indicator status-ok"></span>
                    <strong>Animace:</strong> Implementovány
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">🔧 Test Log</h2>
            <div class="test-log" id="test-log">
                [INFO] Test prostředí inicializováno...<br>
                [OK] CSS styly načteny úspěšně<br>
                [OK] JavaScript funkce připraveny<br>
                [INFO] Připraven k testování...
            </div>
            <button class="test-button" onclick="clearLog()">🗑️ Vymazat Log</button>
            <button class="test-button" onclick="runFullTest()">🚀 Spustit Kompletní Test</button>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/quasar@2.6.0/dist/quasar.umd.prod.js"></script>
    <script>
        // Test functions
        function logMessage(message, type = 'INFO') {
            const log = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${type}] ${timestamp}: ${message}<br>`;
            log.scrollTop = log.scrollHeight;
        }
        
        function testAnimations() {
            logMessage('Restartování CSS animací...', 'TEST');
            
            const elements = [
                { id: 'test-slide', class: 'slide-in-left' },
                { id: 'test-breathe', class: 'breathe' },
                { id: 'test-glow', class: 'glow' },
                { id: 'test-bounce', class: 'bounce-in' }
            ];
            
            elements.forEach(el => {
                const element = document.getElementById(el.id);
                element.classList.remove(el.class);
                setTimeout(() => {
                    element.classList.add(el.class);
                    logMessage(`Animace ${el.class} restartována`, 'OK');
                }, 100);
            });
        }
        
        function testPulse() {
            const element = document.getElementById('pulse-test');
            element.style.animation = 'pulse 2s infinite';
            element.style.setProperty('--pulse-color', '#667eea');
            logMessage('Pulse efekt aktivován', 'TEST');
            
            setTimeout(() => {
                element.style.animation = '';
                logMessage('Pulse efekt ukončen', 'OK');
            }, 4000);
        }
        
        function testShake() {
            const element = document.getElementById('shake-test');
            element.classList.add('shake');
            logMessage('Shake efekt aktivován', 'TEST');
            
            setTimeout(() => {
                element.classList.remove('shake');
                logMessage('Shake efekt ukončen', 'OK');
            }, 500);
        }
        
        function testRipple(event) {
            const element = document.getElementById('ripple-test');
            const ripple = document.createElement('span');
            const rect = element.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = event.clientX - rect.left - size / 2;
            const y = event.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            element.appendChild(ripple);
            logMessage('Ripple efekt vytvořen', 'TEST');
            
            setTimeout(() => {
                ripple.remove();
                logMessage('Ripple efekt dokončen', 'OK');
            }, 600);
        }
        
        function testValueAnimation() {
            const element = document.getElementById('animated-value');
            const start = 0;
            const end = 100;
            const duration = 2000;
            
            logMessage('Spouštím animaci hodnoty 0 → 100', 'TEST');
            
            const startTime = performance.now();
            const animate = (currentTime) => {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                const easeOutCubic = 1 - Math.pow(1 - progress, 3);
                const current = start + (end - start) * easeOutCubic;
                
                element.textContent = Math.round(current);
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    logMessage('Animace hodnoty dokončena', 'OK');
                }
            };
            requestAnimationFrame(animate);
        }
        
        function clearLog() {
            document.getElementById('test-log').innerHTML = '[INFO] Log vymazán...<br>';
        }
        
        function runFullTest() {
            logMessage('=== SPOUŠTÍM KOMPLETNÍ TEST ===', 'INFO');
            
            setTimeout(() => testAnimations(), 500);
            setTimeout(() => testPulse(), 1500);
            setTimeout(() => testShake(), 3000);
            setTimeout(() => testValueAnimation(), 4000);
            
            setTimeout(() => {
                logMessage('=== KOMPLETNÍ TEST DOKONČEN ===', 'OK');
                logMessage('Všechny moderní funkce fungují správně!', 'SUCCESS');
            }, 7000);
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            logMessage('Test prostředí připraveno k použití', 'SUCCESS');
        });
    </script>
</body>
</html>
